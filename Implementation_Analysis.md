# Implementation Analysis: Code<PERSON> vs <PERSON> et al. (2024) and Guide_ops41.md

Revision note (v2)
- Consolidated and de-duplicated sections; removed diagrams and repetitive passages.
- Corrected interpretation of Equation 10: no adaptive α(t) is required by the paper; the code’s min-combination aligns with the paper.
- Grounded every claim with links to the paper, the guide, and repository code; tightened wording.
- Added a prioritized list of all high‑impact gaps (more than three).

Executive summary
- Overall alignment: 78%.
- Implemented correctly: BiGRU backbone, physics-guided loss (paper Eq. 10), robust data pipeline and trainer wiring.
- Missing/partial: additional rock‑physics constraints (empirical VP–VS; multiparameter regression), transfer learning routine, rigorous unit/index handling for physics coupling, and end‑to‑end pseudolabel augmentation for sequence data.
- Most critical blocker: unit- and index-consistent physics coupling (correct VP channel and physical units) used for pseudolabels and physics loss.

Evidence‑based alignment to <PERSON> et al. (2024) and Guide_ops41
- Neural architecture
  - Code: [BiGRU](src/models/neural_networks.py:5) implements bidirectional GRU with hidden_dim=16, num_layers=1, dropout applied post‑GRU, linear head to 1.
  - Paper: use of bi‑GRU described in Methods (network and rationale) [<PERSON> et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](<PERSON> et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:267).
  - Guide: architecture reference and example construction in [Guide_ops41.md](Guide_ops41.md).
- Physics‑guided loss (Eq. 10)
  - Code: implemented as L = L_data + min(L_data, L_physics) in [src/training/losses.py](src/training/losses.py:40) and used by trainer when strategy=="loss_function" [src/training/trainer.py](src/training/trainer.py:56).
  - Paper: Equation 10 Loss = loss_a + min(loss_a, loss_b) (no α) [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604).
  - Guide: presents the same min‑combination in [Guide_ops41.md](Guide_ops41.md).
- Rock‑physics constraints
  - Code: Mudrock line implemented in [src/models/rock_physics/mudrock_line.py](src/models/rock_physics/mudrock_line.py:17); factory registers only "mudrock_line" [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py:7).
  - Paper: three constraints required — mudrock line VP = 1.16×VS + 1.36 (units km/s) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:455); empirical VP–VS fitted from the training well [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474); multiparameter regression VS = a·GR + b·DEN + c·VP + d·RES + e [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489).
  - Status: mudrock line ✓; empirical VP–VS ✗; multiparameter regression ✗.
- Guidance strategies
  - Pseudolabels: helper present in [src/training/strategies.py](src/training/strategies.py:19) but expects 2D features and is not integrated for [B,T,F] sequences across the main pipeline; Guide and paper expect physics‑guided pseudolabels constructed from VP and concatenated as an extra channel [Guide_ops41.md](Guide_ops41.md); [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:511).
  - Loss function: trainer computes physics_pred when strategy=="loss_function" [src/training/trainer.py](src/training/trainer.py:45) and forwards to loss [src/training/trainer.py](src/training/trainer.py:56) — implemented.
  - Transfer learning: strategy enum placeholder only [src/training/strategies.py](src/training/strategies.py:6); no two‑stage pretrain→fine‑tune routine in trainer — missing. Paper describes the process explicitly [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619).
- Data pipeline and training loop
  - Preprocessing supports normalization and sequence tensors [src/data/preprocessing.py](src/data/preprocessing.py:12); trainer handles device, batching, metrics, and evaluation [src/training/trainer.py](src/training/trainer.py:70).
  - Feature set in the paper favors VP+GR+DEN+RES [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:659); the codebase configuration supports configurable features in [Guide_ops41.md](Guide_ops41.md).

Critical and high‑impact gaps (prioritized)
1) Unit‑ and index‑consistent physics coupling (highest priority)
   - Evidence: mudrock model expects VP (km/s) [src/models/rock_physics/mudrock_line.py](src/models/rock_physics/mudrock_line.py:22); trainer extracts VP from features via a fixed index 2 [src/training/trainer.py](src/training/trainer.py:46) and does not denormalize to physical units before calling rock physics; paper’s equations use km/s [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:451).
   - Impact: physics_pred and physics loss can be incorrect if VP is not the correct channel or is scaled; undermines the core physics guidance.
   - Action: look up VP index by feature name from config, denormalize to physical units (m/s→km/s if needed), compute VS_phys, then normalize consistently if required for loss; add assertions/tests.
2) Implement empirical VP–VS (Eq. 6) and multiparameter regression (Eq. 7) constraints
   - Evidence: required by paper [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474), [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489); factory only provides mudrock [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py:7).
   - Impact: limits fidelity to the methodology and best‑performing combo (multiparameter + pseudolabels).
   - Action: add two model classes (fit per training well), register in factory, integrate with pseudolabel and loss strategies.
3) Add transfer learning stage per paper (pretrain on VS_phys → fine‑tune on VS_true)
   - Evidence: paper’s process and LR schedule [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619); code lacks the two‑stage routine.
   - Impact: misses generalization gains when labels are scarce.
   - Action: implement two‑phase trainer flow with reduced LR in fine‑tune; no layer freezing, as per paper.
4) End‑to‑end pseudolabel integration for sequences
   - Evidence: [src/training/strategies.py](src/training/strategies.py:19) expects 2D features; trainer path does not augment [B,T,F] tensors pre‑DataLoader; paper uses pseudolabels as an extra input feature [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:526).
   - Impact: current experiments may not realize the pseudolabel benefit pathway emphasized by the paper.
   - Action: construct VS_phys from unscaled VP prior to normalization, append as a new channel, refit scalers, update model input_dim accordingly.
5) Robust configuration and tests for physics coupling
   - Evidence: hard‑coded VP index in trainer [src/training/trainer.py](src/training/trainer.py:46).
   - Impact: brittle across different feature orders; risks silent misuse.
   - Action: config‑driven feature lookup; unit tests to enforce correct channel and unit handling.
6) Low‑priority hygiene: centralize visualization utilities
   - Evidence: visualizations scattered in scripts/tests; Guide suggests utils module [Guide_ops41.md](Guide_ops41.md).
   - Impact: does not affect methodological fidelity but improves maintainability.

Component‑wise alignment (percent)
- Neural architecture: 100%
- Physics‑guided loss (Eq. 10): 100%
- Rock‑physics constraints coverage: ~33% (1 of 3 implemented)
- Physics‑guidance strategies: ~50% (loss function ✓; pseudolabels partial; transfer learning ✗)
- Data pipeline and preprocessing: ~90%
- Trainer integration and configuration: ~80%
- Overall alignment: 78%

Notes on corrections to earlier draft
- The previous document erroneously required α(t) in the physics‑guided loss. Per the paper’s Equation 10, the correct formulation is Loss = loss_a + min(loss_a, loss_b) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604). The current code matches this [src/training/losses.py](src/training/losses.py:40).

Primary sources cited
- Paper: methodology, constraints, and strategies — equations and procedures
  - Bi‑GRU usage and workflow [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:267)
  - Constraints: mudrock (Eq. 5) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:455); empirical VP–VS (Eq. 6) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474); multiparameter (Eq. 7) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489)
  - Pseudolabels strategy [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:511)
  - Physics‑guided loss (Eq. 10) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604)
  - Transfer learning procedure [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619)
- Guide: architecture, strategies, and intended project structure — [Guide_ops41.md](Guide_ops41.md)

## Concise Completeness Update (v3, 2025-09-20)

Scope
- Reassessed completeness against the core ML architecture in Zhao et al. (2024): hybrid rock-physics constraints, physics-informed loss, network, and feature engineering. Findings are grounded with links to the paper, guide, and repository files.

Summary (concise)
- Overall alignment: 78% (unchanged).
- Implemented: BiGRU backbone; physics-guided loss (Eq. 10) with min-combination; robust data pipeline and trainer wiring; mudrock line constraint.
- Missing/partial: empirical VP–VS (Eq. 6) and multiparameter regression (Eq. 7); end-to-end sequence pseudolabels; transfer learning routine; unit/index consistency for physics coupling; config-driven channel lookup and tests.

What is implemented successfully
- Network and trainer
  - BiGRU backbone for sequence-to-sequence prediction [BiGRU](src/models/neural_networks.py); matches the paper’s architecture and usage [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:267).
  - Physics-aware trainer scaffolding with device/batching/eval [PhysicsGuidedTrainer](src/training/trainer.py).
- Physics-guided loss (Eq. 10)
  - Implemented as L = L_data + min(L_data, L_physics) [PhysicsGuidedLoss](src/training/losses.py); matches Equation 10 (no α) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604).
  - Strategy wiring uses this path when strategy == "loss_function" [PhysicsGuidedTrainer](src/training/trainer.py).
- Rock-physics constraints
  - Mudrock line implemented and available via factory [MudrockLine](src/models/rock_physics/mudrock_line.py); currently the only registered constraint [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py).
- Strategy scaffolding and data pipeline
  - Strategies enum and handler available [PhysicsGuidanceStrategy](src/training/strategies.py), [StrategyHandler](src/training/strategies.py); pseudolabel helper present (2D features).
  - Preprocessing and sequencing operational [src/data/preprocessing.py](src/data/preprocessing.py); aligns with the paper’s workflow [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:230-243).

What is partially or fully missing
- Rock-physics constraints coverage (paper requires 3)
  - Implemented: mudrock line (Eq. 5) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:455).
  - Missing: empirical VP–VS fit (Eq. 6) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474); multiparameter regression (Eq. 7) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489).
- Physics-guided pseudolabels (feature augmentation)
  - Helper expects 2D arrays in [StrategyHandler](src/training/strategies.py); not integrated for sequence tensors [B,T,F]. The paper constructs physics-guided pseudolabels as an extra input channel [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:526-533).
- Transfer learning routine
  - No two-stage pretrain on physics (VS_phys) → fine-tune on true VS with 10× lower LR, no freezing [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619-647).
- Unit and index consistency for physics coupling
  - VP is assumed at a fixed index and used post-normalization in trainer paths [PhysicsGuidedTrainer](src/training/trainer.py), while the equations use physical units (km/s) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:451,455). This risks incorrect physics_pred and loss.
- Config-driven lookups and tests
  - Missing robust, config-based VP channel resolution and unit/shape assertions across physics paths.

Top 1–3 high‑impact gaps (do next)
1) Unit‑ and index‑consistent physics coupling (highest priority)
- Why it matters: Physics equations are defined in km/s (Eq. 5–7) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:451,455). Using normalized VP or a wrong channel silently corrupts physics_pred and the physics‑guided loss, undermining the core contribution.
- Actions:
  - Resolve VP index by feature name from config in [PhysicsGuidedTrainer](src/training/trainer.py); forbid hard‑coded indices.
  - Inverse‑transform VP to physical units before calling rock‑physics models ([MudrockLine](src/models/rock_physics/mudrock_line.py) and future constraints).
  - If needed, re‑normalize VS_phys to the target scale solely for numerical stability in loss; document the transform path.
  - Add assertions and unit tests to validate units and channel selection in all physics paths.

2) Implement empirical VP–VS (Eq. 6) and multiparameter regression (Eq. 7)
- Why it matters: The methodology requires all three constraints; the best performance is achieved by multiparameter regression + pseudolabels [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:723-725).
- Actions:
  - Add two models deriving from [src/models/rock_physics/base.py](src/models/rock_physics/base.py): e.g., EmpiricalVpVs (fit a,b) and MultiParamLinear (fit a,b,c,d,e), per training well.
  - Register in [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py); integrate into strategy wiring (pseudolabels and loss_function).

3) End‑to‑end pseudolabel integration for sequences
- Why it matters: The paper feeds physics‑guided pseudolabels as an extra input channel [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:526-533); current helper is not applied to [B,T,F].
- Actions:
  - Compute VS_phys from unscaled VP prior to normalization in data prep [src/data/preprocessing.py](src/data/preprocessing.py); append as a new channel; refit scalers; update input_dim for [BiGRU](src/models/neural_networks.py).
  - Ensure consistent transform/inference across train/val/test.

Optional 4) Transfer learning per paper (next after 1–3)
- Implement a two‑phase flow in [PhysicsGuidedTrainer](src/training/trainer.py): pretrain on physics labels (VS_phys), then fine‑tune on true VS with LR reduced 10×, no layer freezing [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619-647).

Percent alignment snapshot
- Neural architecture: 100% (bi‑GRU) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:267)
- Physics‑guided loss (Eq. 10): 100% (min combination) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604)
- Rock‑physics constraints coverage: ~33% (1/3 implemented)
- Physics‑guidance strategies: ~50% (loss function ✓; pseudolabels partial; transfer learning ✗)
- Data pipeline and preprocessing: ~90%
- Trainer integration and configuration: ~80%
- Overall alignment: 78%

Primary references
- Workflow and bi‑GRU usage [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:230-243,267)
- Constraints: mudrock (Eq. 5) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:455); empirical VP–VS (Eq. 6) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474); multiparameter (Eq. 7) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489)
- Pseudolabels construction [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:526-533)
- Physics‑guided loss (Eq. 10) [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604)
- Transfer learning process [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619-647)
- Guide reference [Guide_ops41.md](Guide_ops41.md)

## Comprehensive High‑Impact Gaps (Implementation Checklist)

Purpose
- This checklist enumerates all high‑impact items required to faithfully reproduce Zhao et al. (2024) across constraints, strategies, data handling, and evaluation. Each item includes the rationale and concrete implementation locations.

1) Unit‑ and index‑consistent physics coupling (Highest priority)
- Why: Physics equations (Eq. 5–7) are defined in km/s; using normalized VP or wrong channel corrupts physics_pred and the physics‑guided loss, undermining the core contribution.
- Actions:
  - Resolve VP channel by feature name from config instead of hard‑coding index 2 in [src/training/trainer.py](src/training/trainer.py).
  - Inverse‑transform VP to physical units before rock‑physics calls using fitted scalers from [src/data/preprocessing.py](src/data/preprocessing.py).
  - Compute VS_phys with the selected constraint ([MudrockLine](src/models/rock_physics/mudrock_line.py), and future models), then optionally normalize VS_phys consistently for loss stability.
  - Add assertions/tests that fail fast if units or channels are inconsistent (see [tests](tests/)).
- Evidence: current fixed index and lack of denormalization [src/training/trainer.py](src/training/trainer.py); physics requires km/s [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:451,455).

2) Implement the two missing constraints: empirical VP–VS (Eq. 6) and multiparameter regression (Eq. 7)
- Why: Paper evaluates 3 constraints; best performance comes from multiparameter regression + pseudolabels.
- Actions:
  - Add EmpiricalVpVs(a,b) and MultiParamLinear(a,b,c,d,e) classes derived from [src/models/rock_physics/base.py](src/models/rock_physics/base.py).
  - Fit coefficients per training well; persist fitted params with the run artifacts.
  - Register in [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py) and expose selection via config.
  - Integrate with loss_function and pseudolabels strategies.
- Evidence: constraints II and III are required (Eq. 6–7) and currently missing.

3) End‑to‑end physics‑guided pseudolabels for sequences [B,T,F]
- Why: The paper constructs physics‑guided pseudolabels and feeds them as an extra input channel; current helper is 2D‑only and not applied to sequence tensors.
- Actions:
  - In data prep ([src/data/preprocessing.py](src/data/preprocessing.py)), compute VS_phys from unscaled VP, append as a new channel to features before normalization; refit scalers including the added channel.
  - Update model input_dim in [BiGRU](src/models/neural_networks.py) when pseudolabels are enabled.
  - Ensure consistent transforms for train/val/test and in any export/inference path.
- Evidence: sequence augmentation missing; paper section on pseudolabels.

4) Two‑stage transfer learning routine (pretrain → fine‑tune)
- Why: Paper’s yellow‑box strategy improves generalization when labels are scarce.
- Actions:
  - Pretrain: inputs = logging features, target = VS_phys from the chosen constraint; train until convergence.
  - Fine‑tune: reinitialize optimizer with 10× lower LR; target = VS_true; no layer freezing; full‑network update.
  - Implement in [src/training/trainer.py](src/training/trainer.py) with clear config toggles and logging of both stages.
- Evidence: detailed process and LR schedule in the paper.

5) Config‑driven feature mapping and physics safety checks
- Why: Avoid silent misuse due to feature re‑ordering or missing channels.
- Actions:
  - Add feature name→index mapping and units metadata in configs (e.g., [configs/default_config.yaml](configs/default_config.yaml)); resolve indices at runtime in [src/training/trainer.py](src/training/trainer.py).
  - Add config flags for resistivity log10 transform and unit converters; validate at startup.
  - Unit tests in [tests](tests/) to cover mapping and unit assertions.
- Evidence: current fixed index; no formal unit metadata.

6) Normalization policy alignment with the paper (features and pseudolabels)
- Why: Paper normalizes to (−1,1); resistivity uses log10; inconsistency here affects both learning dynamics and evaluation comparability.
- Actions:
  - Ensure MinMax scaling to (−1,1) on training well(s) consistently in [src/data/preprocessing.py](src/data/preprocessing.py); apply the same transform to val/test.
  - Apply log10 to RES before scaling if enabled by config; document in Guide.
  - Normalize appended VS_phys channel with the same scaler fit regime; denormalize to physical units for metrics/plots.
- Evidence: Data and Methodology sections (normalization and RES transform).

7) Experiment orchestration for full grid of constraints × strategies with blind‑test protocol
- Why: The paper evaluates 3 constraints × 3 strategies across five train‑blind splits; a repeatable harness is needed to replicate aggregate statistics.
- Actions:
  - Implement an experiment runner (script or class) to sweep all combinations and each choice of training well, with 80/20 train/val on the training well and blind tests on the other four wells.
  - Persist per‑well results and averages to JSON/CSV in output/results/; capture configs and fitted physics coefficients.
  - Optionally extend [examples/train_model.py](examples/train_model.py) or add a dedicated runner in examples/.
- Evidence: paper’s Results section and Figure 7 protocol.

8) Metrics and evaluation alignment on physical units
- Why: Report RMSE and Pearson correlation in physical units; mixed scaling yields misleading comparisons.
- Actions:
  - In [PhysicsGuidedTrainer.evaluate](src/training/trainer.py), ensure predictions/targets are denormalized to km/s before RMSE and correlation; clearly report both physical and normalized metrics if needed.
  - Provide per‑well and averaged metrics across blind wells; store in artifacts.
- Evidence: metrics and crossplots in the paper.

9) Rock‑physics factory and sequence inference robustness
- Why: Expand beyond mudrock; ensure sequence‑shape inference and NaN handling are robust for all constraints.
- Actions:
  - Extend [src/models/rock_physics/__init__.py](src/models/rock_physics/__init__.py) to select by name and instantiate with fitted params.
  - Provide vectorized predict methods compatible with batch/time dimensions and guard against NaNs/edge cases.
  - Add unit tests in [tests/test_rock_physics.py](tests/test_rock_physics.py) for the new models with sequence inputs.

10) Reproducibility and artifact completeness
- Why: Reliable replication requires deterministic seeds and complete artifacts.
- Actions:
  - Set global seeds and document determinism caveats; store train/val splits and the selected training well id in run metadata.
  - Save: model checkpoints (pretrain and fine‑tune), fitted scalers, physics coefficients, config, and evaluation results under output/.
  - Add a minimal "replay" script to load artifacts and reproduce evaluation/plots.

11) Centralize visualizations (medium priority)
- Why: Maintainability and consistent plots for comparisons and papers.
- Actions:
  - Move plotting utilities under [src/utils](src/utils/) and implement standardized figures (prediction curves, crossplots, error bars, and summary charts analogous to Figures 6–12).
  - Ensure plots use denormalized physical units and consistent axis limits.

Priority guidance
- Highest impact to scientific fidelity: items 1–4.
- Required to reproduce aggregate results: items 5–10.
- Hygiene/maintainability: item 11.

This checklist complements and expands the earlier "Critical and high‑impact gaps (prioritized)" section by adding orchestration, normalization policy alignment, metrics on physical units, robust factory integration, and reproducibility artifacts.

## Architecture diagrams: Current vs Paper method

Note
- The following Mermaid diagrams purposefully avoid double quotes and parentheses inside bracketed node labels to ensure correct rendering.

Current implemented codebase
```mermaid
flowchart TB
  subgraph Current codebase
    A[Inputs GR DEN VP RES] --> B[Preprocess clean normalize minus1 to 1]
    B --> C[Sequence builder B T F]
    C --> D[BiGRU hidden 16 single layer]
    D --> E[Predictions VS hat]
    %% Physics loss path with mudrock line only
    C --> RP1[Mudrock line]
    RP1 --> P_pred[VS phys from VP channel]
    D --> L1[PhysicsGuidedLoss Eq10 min]
    P_pred --> L1
    L1 --> Train[Optimization loop]
    %% Known gaps
    Note1[VP channel fixed index and units normalized risk]
    Note2[Pseudolabels extra channel missing]
    Note3[Transfer learning missing]
  end

  classDef imp fill:#e6ffe6,stroke:#228B22,stroke-width:1px;
  classDef risk fill:#fff3cd,stroke:#ff9800,stroke-width:2px;
  classDef miss fill:#ffe6e6,stroke:#d32f2f,stroke-width:2px;

  class A,B,C,D,E,RP1,P_pred,L1,Train imp;
  class Note1 risk;
  class Note2,Note3 miss;
```

Trace to code
- Trainer and loss: [PhysicsGuidedTrainer](src/training/trainer.py), [PhysicsGuidedLoss](src/training/losses.py)
- Rock physics and model: [MudrockLine](src/models/rock_physics/mudrock_line.py), [BiGRU](src/models/neural_networks.py)
- Strategies scaffold: [StrategyHandler](src/training/strategies.py)
- Data prep and normalization: [src/data/preprocessing.py](src/data/preprocessing.py)

Paper method target architecture
```mermaid
flowchart TB
  subgraph Paper method Zhao 2024
    A1[Inputs GR DEN VP RES] --> B1[Remove outliers normalize minus1 to 1 log10 RES]
    B1 --> C1[Sequence builder B T F]
    %% Three constraints
    C1 --> M1[Mudrock line]
    C1 --> M2[Empirical Vp Vs fit from training well]
    C1 --> M3[Multiparameter regression fit]
    %% Physics guided pseudolabel channel
    M1 --> PL1[VS phys channel]
    M2 --> PL1
    M3 --> PL1
    PL1 --> Aug1[Pseudolabels add as extra input channel]
    %% Feature selection and merge
    B1 --> FSel1[Feature set VP GR DEN RES]
    FSel1 --> Merge1[Concatenate features with pseudolabel channel]
    Aug1 --> Merge1
    %% Network and strategies
    Merge1 --> D1[BiGRU]
    D1 --> L2[PhysicsGuidedLoss Eq10 min]
    D1 --> TL1[Transfer learning pretrain on VS phys]
    TL1 --> TL2[Fine tune on VS true lr reduced ten x no freezing]
    L2 --> Opt1[Optimization]
    TL2 --> Opt1
    %% Evaluation protocol
    Opt1 --> Eval1[Evaluate RMSE correlation in km per s]
    Eval1 --> Blind1[Blind tests train one well test four wells]
  end
```

Trace to paper
- Workflow and normalization policy: [Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:230-243)
- Constraints: mudrock Eq 5 [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:455), empirical VP VS Eq 6 [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:474), multiparameter regression Eq 7 [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:489)
- Pseudolabels construction: [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:526-533)
- Physics guided loss Eq 10 min combination: [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:604)
- Transfer learning pretrain and fine tune protocol: [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:619-647)
- Blind test evaluation across wells and metrics in physical units: [Zhao et al. ... pdf](Zhao et al. - 2024 - Rock-physics-guided machine learning for shear son.pdf:657-747)
